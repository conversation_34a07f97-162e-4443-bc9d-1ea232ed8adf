import 'package:flutter_test/flutter_test.dart';
import 'package:novel_app/langchain/chains/detailed_outline_chain.dart';
import 'package:novel_app/langchain/services/lightweight_generation_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';
import 'package:get/get.dart';

/// 测试细纲生成的连贯性保证机制
void main() {
  group('DetailedOutlineChain 连贯性测试', () {
    test('DetailedOutlineChain 应该有 Memory 配置', () {
      // 这个测试需要实际的LLM配置，这里只是结构测试
      // 在实际环境中，需要配置有效的API密钥
      
      // 验证 DetailedOutlineChain 的构造函数包含 Memory
      expect(() {
        // 这里需要一个mock LLM，实际测试时需要配置
        // final chain = DetailedOutlineChain(llm: mockLLM);
        // expect(chain.memory, isNotNull);
      }, returnsNormally);
    });

    test('提示词模板应该包含 history 参数', () {
      // 验证提示词模板包含必要的参数
      final template = '''
你是一位专业且权威的爽文小说编辑和策划师。请使用中文回复。
任务：根据以下提供的小说整体设定和指定章节的初步信息，为**第 {chapterNumber} 章**生成详细的情节细纲。

**历史上下文和已生成细纲:**
{history}
''';
      
      expect(template.contains('{history}'), isTrue);
      expect(template.contains('{chapterNumber}'), isTrue);
    });

    test('Memory 信息构建应该包含关键事件时间线', () {
      final mockOutlineBuffer = '''
=== 已生成的章节细纲和关键事件时间线 ===
**重要提醒：严格禁止重复以下已发生的情节和事件！**

### 关键事件时间线（已发生）：
- 第1章：某某事件发生
- 第2章：某某情节发展

### 第1章：某某标题（前文章节 - 已发生）
**状态：已完成，不可重复**
详细细纲内容...
---
''';
      
      expect(mockOutlineBuffer.contains('严格禁止重复'), isTrue);
      expect(mockOutlineBuffer.contains('关键事件时间线'), isTrue);
      expect(mockOutlineBuffer.contains('已完成，不可重复'), isTrue);
    });
  });

  group('连贯性检查机制测试', () {
    test('应该能检测重复情节', () {
      final previousOutlines = [
        '第1章：赵惊从大牢被请出来，成为扶苏的老师',
        '第2章：赵惊进行第一次授课',
      ];
      
      final newOutline = '第3章：赵惊从大牢被请出来'; // 重复情节
      
      // 这里应该有逻辑检测重复
      bool hasRepetition = previousOutlines.any((prev) => 
        prev.contains('从大牢被请出来') && newOutline.contains('从大牢被请出来'));
      
      expect(hasRepetition, isTrue);
    });

    test('应该能构建正确的时间线', () {
      final chapters = {
        1: '第1章细纲：赵惊被捕入狱',
        2: '第2章细纲：扶苏接到父皇命令',
        3: '第3章细纲：赵惊被请出大牢',
      };
      
      // 对于第4章，应该能看到前面的时间线
      final currentChapter = 4;
      final timeline = <String>[];
      
      for (int i = 1; i < currentChapter; i++) {
        if (chapters.containsKey(i)) {
          timeline.add('第${i}章：${chapters[i]}');
        }
      }
      
      expect(timeline.length, equals(3));
      expect(timeline.last.contains('第3章'), isTrue);
    });
  });

  group('Memory 机制验证', () {
    test('Memory 保存和加载应该正常工作', () async {
      // 这个测试需要实际的Memory实现
      // 在实际环境中需要配置Hive等存储
      
      // 模拟Memory操作
      final mockMemoryData = <String, String>{};
      
      // 保存操作
      mockMemoryData['input'] = '系统：加载已有章节细纲';
      mockMemoryData['output'] = '第1章细纲内容...';
      
      // 验证保存
      expect(mockMemoryData.containsKey('input'), isTrue);
      expect(mockMemoryData.containsKey('output'), isTrue);
      
      // 加载操作
      final history = mockMemoryData['output'];
      expect(history, isNotNull);
      expect(history!.contains('第1章'), isTrue);
    });
  });
}

/// 集成测试 - 需要在实际环境中运行
void integrationTest() {
  group('细纲生成集成测试', () {
    test('连续生成多个章节应该保持连贯性', () async {
      // 这个测试需要实际的API配置和数据库
      // 在实际环境中运行以验证修复效果
      
      /*
      final service = LightweightGenerationService(
        apiConfigController: Get.find<ApiConfigController>(),
        vectorizationService: Get.find<NovelVectorizationService>(),
      );
      
      // 生成第1章细纲
      final chapter1 = await service.generateDetailedChapterOutline(
        novelTitle: '测试小说',
        chapterNumber: 1,
        chapterTitle: '第一章',
        chapterSummary: '故事开始',
        genres: ['玄幻'],
        theme: '成长',
        targetReaders: '年轻读者',
      );
      
      // 生成第2章细纲
      final chapter2 = await service.generateDetailedChapterOutline(
        novelTitle: '测试小说',
        chapterNumber: 2,
        chapterTitle: '第二章',
        chapterSummary: '情节发展',
        genres: ['玄幻'],
        theme: '成长',
        targetReaders: '年轻读者',
      );
      
      // 验证第2章不会重复第1章的情节
      expect(chapter1, isNotNull);
      expect(chapter2, isNotNull);
      expect(chapter1 != chapter2, isTrue);
      
      // 这里可以添加更多具体的连贯性检查
      */
    });
  });
}
