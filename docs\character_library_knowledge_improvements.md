# 角色类型管理、书库批量操作和知识库优化改进

## 概述

本次改进主要针对三个核心功能进行了优化：

1. **角色类型管理系统** - 支持一个角色类型选择多个角色
2. **我的书库** - 添加批量选择、删除和导出功能
3. **知识库选择逻辑** - 优化用户体验，添加搜索和筛选功能

## 1. 角色类型管理系统优化

### 改进前
- 一个角色类型只能选择一个角色卡片
- 数据结构：`Map<String, CharacterCard>`

### 改进后
- 一个角色类型可以选择多个角色卡片
- 数据结构：`Map<String, List<CharacterCard>>`
- 保持向后兼容性

### 主要变更

#### 数据结构更新
```dart
// 旧版本
final Map<String, CharacterCard> selectedCharacterCards = <String, CharacterCard>{}.obs;

// 新版本
final Map<String, List<CharacterCard>> selectedCharacterCards = <String, List<CharacterCard>>{}.obs;
```

#### 新增方法
```dart
// 添加角色卡片到指定类型
void addCharacterCard(String typeId, CharacterCard card)

// 移除指定角色卡片
void removeCharacterCard(String typeId, CharacterCard card)

// 获取指定类型的所有角色卡片
List<CharacterCard> getCharacterCards(String typeId)

// 兼容旧API的转换方法
Map<String, CharacterCard> getCompatibleCharacterCards()
```

#### 界面改进
- 角色选择对话框支持多选（复选框）
- 显示已选择角色数量
- 支持批量选择和取消选择
- 角色卡片列表显示所有选中的角色

### 使用示例
```dart
// 选择角色类型
controller.toggleCharacterType(heroType);

// 添加多个角色到同一类型
controller.addCharacterCard(heroType.id, hero1);
controller.addCharacterCard(heroType.id, hero2);

// 获取该类型的所有角色
final heroes = controller.getCharacterCards(heroType.id);
```

## 2. 我的书库批量操作功能

### 新增功能
- **多选模式**：长按或点击多选按钮进入多选模式
- **批量删除**：选择多本小说进行批量删除
- **批量导出**：支持批量导出为TXT或Markdown格式
- **全选功能**：一键选择所有小说

### 界面改进
- 多选模式下显示复选框
- 顶部显示已选择数量
- 操作按钮：全选、批量删除、批量导出、退出多选
- 选中项高亮显示

### 操作流程
1. **进入多选模式**
   - 长按任意小说卡片
   - 或点击工具栏的多选按钮

2. **选择小说**
   - 点击复选框或卡片进行选择
   - 支持全选功能

3. **批量操作**
   - 批量删除：确认对话框防止误操作
   - 批量导出：选择格式后统一导出

### 代码实现
```dart
// 多选模式状态
final RxBool isMultiSelectMode = false.obs;
final RxSet<Novel> selectedNovels = <Novel>{}.obs;

// 进入/退出多选模式
void _enterMultiSelectMode()
void _exitMultiSelectMode()

// 批量操作
Future<void> _batchDeleteNovels()
Future<void> _batchExportNovels(String format)
```

## 3. 知识库选择逻辑优化

### 用户体验改进
- **搜索功能**：支持按标题、内容、分类搜索
- **分类筛选**：可按分类快速筛选文档
- **视觉优化**：改进卡片设计，添加分类颜色标识
- **交互优化**：长按进入多选模式，右键菜单操作

### 界面改进

#### 搜索栏
```dart
TextField(
  decoration: InputDecoration(
    hintText: '搜索知识文档...',
    prefixIcon: Icon(Icons.search),
    suffixIcon: clearButton,
  ),
  onChanged: (value) => searchQuery.value = value,
)
```

#### 分类筛选
- 显示每个分类的文档数量
- 支持"全部"选项
- 选中状态高亮显示

#### 文档卡片优化
- 分类颜色标识（圆形头像）
- 内容预览（最多2行）
- 更新时间显示
- 右键菜单操作

### 筛选逻辑
```dart
List<KnowledgeDocument> _getFilteredDocuments() {
  var docs = controller.documents.toList();
  
  // 按分类筛选
  if (selectedCategory.value != '全部') {
    docs = docs.where((doc) => doc.category == selectedCategory.value).toList();
  }
  
  // 按搜索关键词筛选
  if (searchQuery.value.isNotEmpty) {
    final query = searchQuery.value.toLowerCase();
    docs = docs.where((doc) {
      return doc.title.toLowerCase().contains(query) ||
             doc.content.toLowerCase().contains(query) ||
             doc.category.toLowerCase().contains(query);
    }).toList();
  }
  
  // 按更新时间排序
  docs.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  
  return docs;
}
```

## 4. 兼容性保证

### 向后兼容
- 保留原有的单选方法 `setCharacterCard()`
- 提供 `getFirstCharacterCard()` 方法获取第一个角色
- 自动转换数据格式以兼容现有API

### 数据迁移
- 现有数据自动转换为新格式
- 不影响已保存的小说和角色数据

## 5. 测试验证

### 单元测试
创建了完整的单元测试覆盖：
- 多角色选择功能
- 角色卡片管理
- 数据格式转换
- 兼容性验证

### 功能测试
- 角色多选界面测试
- 书库批量操作测试
- 知识库搜索筛选测试

## 6. 使用指南

### 角色多选使用
1. 在小说设置中选择角色类型
2. 点击"选择角色卡片"按钮
3. 在对话框中勾选多个角色
4. 点击"确定"保存选择

### 书库批量操作使用
1. 在书库界面长按任意小说进入多选模式
2. 选择需要操作的小说
3. 点击顶部的批量删除或导出按钮
4. 确认操作完成

### 知识库优化使用
1. 使用搜索栏快速查找文档
2. 点击分类标签筛选特定类型文档
3. 长按文档进入多选模式
4. 使用右键菜单进行快速操作

## 7. 技术细节

### 关键文件修改
- `lib/controllers/novel_controller.dart` - 角色多选逻辑
- `lib/screens/home/<USER>
- `lib/screens/library/library_screen.dart` - 书库批量操作
- `lib/screens/knowledge_base_screen.dart` - 知识库优化

### 性能优化
- 使用响应式编程（GetX）确保界面实时更新
- 优化列表渲染性能
- 合理的数据结构设计减少内存占用

## 8. 未来扩展

### 可能的改进方向
1. 角色关系图谱可视化
2. 更多批量操作选项（批量编辑、批量分类）
3. 知识库智能推荐
4. 导出格式扩展（PDF、EPUB等）

这些改进显著提升了用户体验，使得角色管理更加灵活，书库操作更加高效，知识库使用更加便捷。
