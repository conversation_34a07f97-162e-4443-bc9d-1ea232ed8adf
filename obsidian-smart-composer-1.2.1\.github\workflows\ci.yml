name: CI

on:
  pull_request:
    branches: [main]

jobs:
  check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm ci
      - name: Type check
        run: npm run type:check
      - name: Lint check
        run: npm run lint:check
      - name: Test
        run: npm test
