# 岱宗AI辅助创作助手 - Cursor风格编辑器使用指南

## 概述

岱宗AI辅助创作助手现已升级为类似Cursor IDE的智能编辑体验，支持AI直接对小说内容进行修改建议，并提供diff视图预览和精细化控制。

## 主要功能

### 1. 双模式操作

#### 聊天模式
- AI仅提供建议和指导，不直接修改内容
- 适合讨论情节、角色设定、写作技巧等
- 界面显示聊天气泡图标

#### 创作模式  
- AI可以直接对章节内容进行修改建议
- 提供结构化的修改建议和diff预览
- 用户可以精确控制每个修改的接受或拒绝
- 界面显示编辑图标

### 2. Diff视图系统

#### 颜色标识
- **绿色**: 新增的文本内容
- **红色**: 删除的文本内容  
- **橙色**: 修改的文本内容
- **灰色**: 未修改的文本内容

#### 行号显示
- 显示原始文本和修改后文本的行号对应关系
- 便于精确定位修改位置

### 3. 精细化控制

#### 单个修改控制
- 每个修改建议都有独立的"接受"和"拒绝"按钮
- 可以查看修改原因说明
- 支持部分接受修改建议

#### 批量操作
- "全部接受"：一键接受所有待处理的修改
- "全部拒绝"：一键拒绝所有待处理的修改
- 显示待处理修改数量统计

### 4. 修改建议类型

#### 替换 (replace)
- 替换指定行范围的文本内容
- 适用于改写、润色、修正等场景

#### 插入 (insert)  
- 在指定位置插入新的文本内容
- 适用于续写、补充描述等场景

#### 删除 (delete)
- 删除指定行范围的文本内容
- 适用于精简、去除冗余等场景

## 使用流程

### 1. 模式切换
1. 在岱宗AI助手界面顶部找到模式切换器
2. 点击"聊天模式"或"创作模式"进行切换
3. 创作模式会自动保存当前章节内容作为原始版本

### 2. 发送编辑指令
在创作模式下，可以发送如下类型的指令：

#### 续写指令
```
请为这一章续写500字，描述主角的内心独白
在章节末尾添加一个转折情节
```

#### 修改指令  
```
请润色第3-5段的对话，使其更加生动
将环境描写改得更加细腻
优化开头段落的节奏感
```

#### 删除指令
```
删除第2段中的冗余描述
去掉重复的心理描写
移除不必要的过渡句
```

### 3. 查看修改建议
1. AI会返回结构化的修改建议
2. 在聊天界面中显示diff预览
3. 每个修改都有详细的说明和原因

### 4. 处理修改建议
1. 逐个查看每项修改建议
2. 点击"接受"或"拒绝"按钮
3. 或使用"全部接受"/"全部拒绝"进行批量操作

### 5. 应用修改
1. 接受修改后，点击"应用修改"按钮
2. 系统会将已接受的修改应用到实际的章节内容中
3. 编辑器中的文本会立即更新

## 高级功能

### 1. 章节引用 (@语法)
在创作模式下，可以使用@语法引用其他章节：

```
@第1章 请参考第一章的写作风格来优化当前章节
@1 @3 结合第1章和第3章的情节来续写
```

### 2. 上下文感知
- AI会自动考虑整本小说的情节连贯性
- 保持角色设定和世界观的一致性
- 利用langchain记忆参数传递章节内容

### 3. 修改历史
- 所有修改都有完整的历史记录
- 可以查看修改前后的对比
- 支持撤销已应用的修改

## 最佳实践

### 1. 明确的指令
- 使用具体、明确的编辑指令
- 指定要修改的段落或内容范围
- 说明修改的目的和期望效果

### 2. 渐进式修改
- 避免一次性进行大量修改
- 先处理结构性问题，再进行细节润色
- 分批次接受和应用修改建议

### 3. 保持连贯性
- 在修改前考虑对整体情节的影响
- 确保修改后的内容与前后文保持一致
- 利用章节引用功能保持上下文关联

## 注意事项

1. **备份重要内容**: 在进行大量修改前，建议先备份原始内容
2. **仔细审查**: 每个修改建议都应仔细审查后再接受
3. **测试连贯性**: 应用修改后，检查章节的整体连贯性
4. **模式切换**: 根据需要及时在聊天模式和创作模式间切换

## 故障排除

### 常见问题

1. **修改建议不准确**: 尝试提供更具体的指令和上下文
2. **diff显示异常**: 检查原始内容是否正确保存
3. **应用修改失败**: 确认修改建议的格式正确性

### 技术支持

如遇到技术问题，请检查：
- 网络连接是否正常
- AI模型配置是否正确
- 章节内容是否已正确加载

通过以上功能，岱宗AI辅助创作助手为您提供了专业级的小说编辑体验，让AI成为您创作路上的得力助手。
