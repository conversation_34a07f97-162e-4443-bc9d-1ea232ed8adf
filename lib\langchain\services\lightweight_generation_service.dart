import 'dart:convert';
import 'dart:math';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';
import 'package:novel_app/langchain/chains/novel_generation_chain.dart';
import 'package:novel_app/langchain/chains/detailed_outline_chain.dart';
import 'package:novel_app/langchain/utils/model_adapter.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/services/embedding_service.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';
import 'package:novel_app/models/chat_message.dart' as app_chat;
import 'package:langchain/langchain.dart';

/// 精简生成服务
/// 只获取细纲的记忆，不保存具体章节内容的记忆，以提高效率并降低成本
class LightweightGenerationService extends GetxService {
  final ApiConfigController _apiConfigController;
  late final ChatHistoryService _chatHistoryService;
  late final EmbeddingService _embeddingService;
  final NovelVectorizationService _vectorizationService;

  // 缓存生成链
  final Map<String, NovelGenerationChain> _chainCache = {};
  final Map<String, DetailedOutlineChain> _detailedChainCache = {};

  // 大纲生成批次大小
  static const int _outlineBatchSize = 20;

  LightweightGenerationService({
    required ApiConfigController apiConfigController,
    required NovelVectorizationService vectorizationService,
  })  : _apiConfigController = apiConfigController,
        _vectorizationService = vectorizationService {
    // 延迟初始化依赖服务
    _chatHistoryService = Get.find<ChatHistoryService>();
    _embeddingService = Get.find<EmbeddingService>();
  }

  /// 创建或获取已有的生成链
  Future<NovelGenerationChain> _getChain(String novelTitle) async {
    if (_chainCache.containsKey(novelTitle)) {
      return _chainCache[novelTitle]!;
    }
    final modelConfig = _apiConfigController.getCurrentModel();
    final llm = ModelAdapter.createLLMFromConfig(modelConfig);
    final chain = NovelGenerationChain(llm: llm, novelTitle: novelTitle);
    _chainCache[novelTitle] = chain;
    return chain;
  }

  /// 创建或获取已有的详细大纲生成链
  Future<DetailedOutlineChain> _getDetailedChain(String novelTitle) async {
    if (_detailedChainCache.containsKey(novelTitle)) {
      return _detailedChainCache[novelTitle]!;
    }

    // 获取当前模型配置
    final modelConfig = _apiConfigController.getCurrentModel();

    // 检查是否是阿里云通义千问模型
    bool isAliyunQwen = modelConfig.apiUrl.contains('dashscope.aliyuncs.com') ||
        modelConfig.name.contains('阿里') ||
        modelConfig.name.contains('通义');

    if (isAliyunQwen) {
      print(
          "[LightweightGenerationService._getDetailedChain] 检测到阿里云通义千问模型，确保启用流式输出");
    }

    // 创建LLM
    final llm = ModelAdapter.createLLMFromConfig(modelConfig);

    // 创建并缓存链
    final chain = DetailedOutlineChain(llm: llm);
    _detailedChainCache[novelTitle] = chain;
    return chain;
  }

  /// Helper to get NovelMemory associated with a novel title
  NovelMemory _getNovelMemory(String novelTitle, {String? sessionId}) {
    return NovelMemory(novelTitle: novelTitle, sessionId: sessionId);
  }

  /// 构建角色详情字符串
  String _buildCharacterDetailsString(
      Map<String, CharacterCard> characterCards) {
    if (characterCards.isEmpty) {
      return '';
    }

    final buffer = StringBuffer();
    buffer.writeln('# 角色设定');

    characterCards.forEach((_, card) {
      buffer.writeln('## ${card.name}');
      buffer.writeln('- 性别: ${card.gender}');
      buffer.writeln('- 年龄: ${card.age}');
      buffer.writeln('- 外貌: ${card.appearance}');
      buffer.writeln('- 性格: ${card.personalityTraits}');
      buffer.writeln('- 背景: ${card.background}');
      buffer.writeln(
          '- 目标: ${card.shortTermGoals ?? ''} ${card.longTermGoals ?? ''}');
      buffer.writeln('');
    });

    return buffer.toString();
  }

  /// 生成小说大纲 (分批生成版本)
  /// 精简模式也支持分批生成，避免大章节数时的问题
  Future<String> generateOutline({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(String)? onProgress,
    void Function(String)? onRealtimeOutput,
    String? sessionId,
  }) async {
    if (totalChapters <= 0) {
      throw ArgumentError('总章节数必须大于 0。');
    }

    print(
        "[LightweightGenerationService.generateOutline] 开始为 '$novelTitle' ($totalChapters 章) 生成分批大纲（精简模式）。批次大小: $_outlineBatchSize");

    // 实时输出初始信息
    onRealtimeOutput?.call("开始生成《$novelTitle》的大纲（精简模式）\n");
    onRealtimeOutput?.call("总章节数: $totalChapters章\n");
    onRealtimeOutput?.call("批次大小: $_outlineBatchSize章/批次\n");

    try {
      final chain = await _getChain(novelTitle);
      final novelMemory = _getNovelMemory(novelTitle, sessionId: sessionId);

      // 准备基础输入参数
      String charactersString =
          _buildCharacterDetailsString(characterCards ?? {});
      String writingStylePromptString = writingStyle?.getPrompt() ?? '';
      String knowledgeBaseString = '';

      final knowledgeBaseController = Get.find<KnowledgeBaseController>();
      if (knowledgeBaseController.useKnowledgeBase.value &&
          knowledgeBaseController.selectedDocIds.isNotEmpty) {
        knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
      }

      final baseInput = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'theme': theme ?? '无主题',
        'targetReaders': targetReaders ?? '通用',
        'totalChapters': totalChapters.toString(),
        'background': background ?? '无背景',
        'otherRequirements': otherRequirements ?? '无其他要求',
        'characters': charactersString,
        'writingStylePrompt': writingStylePromptString.isNotEmpty
            ? writingStylePromptString
            : '无特定文风要求',
        'knowledgeBase':
            knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
        'task': 'generate_outline',
      };

      List<Map<String, dynamic>> allChapters = [];
      int numberOfBatches = (totalChapters / _outlineBatchSize).ceil();

      // 实时输出批次信息
      onRealtimeOutput?.call("计算得出需要 $numberOfBatches 个批次\n\n");

      for (int i = 0; i < numberOfBatches; i++) {
        int startChapter = i * _outlineBatchSize + 1;
        int endChapter = min((i + 1) * _outlineBatchSize, totalChapters);
        print(
            "[LightweightGenerationService.generateOutline] 处理批次 ${i + 1}/$numberOfBatches (章节 $startChapter-$endChapter)...");

        // 实时输出当前批次信息
        onRealtimeOutput?.call(
            "� 正在处理批次 ${i + 1}/$numberOfBatches (第$startChapter-$endChapter章)...\n");

        // 如果是续写模式（第一个批次且起始章节大于1），将已有大纲信息添加到 Memory 中
        if (i == 0 && startChapter > 1) {
          onRealtimeOutput?.call("📖 使用langchain memory隐性传递前文上下文\n");
          await _addExistingOutlineToMemory(chain, novelTitle, sessionId);
        }

        final batchInput = {
          ...baseInput,
          'startChapter': startChapter.toString(),
          'endChapter': endChapter.toString(),
          'input': "生成《$novelTitle》第$startChapter到第$endChapter章的大纲",
        };

        int retryCount = 0;
        const int maxRetries = 2;
        bool batchSuccess = false;

        while (retryCount <= maxRetries && !batchSuccess) {
          try {
            print(
                "[LightweightGenerationService.generateOutline] 使用流式模式生成批次 ${i + 1} (尝试 ${retryCount + 1})...");

            // 实时输出重试信息
            if (retryCount > 0) {
              onRealtimeOutput
                  ?.call("⚠️ 批次 ${i + 1} 第${retryCount + 1}次尝试...\n");
            } else {
              onRealtimeOutput?.call("🔄 开始生成批次 ${i + 1}...\n");
            }

            // 使用chain.run()而不是stream()，确保memory机制正常工作
            final rawResult = await chain.run(batchInput);
            print(
                "[LightweightGenerationService.generateOutline] 批次 ${i + 1} (尝试 ${retryCount + 1}) 原始结果长度: ${rawResult.length}");

            // 实时输出生成结果长度
            onRealtimeOutput
                ?.call("📄 批次 ${i + 1} 生成完成，内容长度: ${rawResult.length}\n");

            // 解析批次结果
            dynamic decodedResult;
            try {
              String cleanedResult = rawResult.trim();

              // 清理Grok模型的<think>标签
              cleanedResult = _cleanThinkTags(cleanedResult);

              // 清理可能的markdown标记
              if (cleanedResult.startsWith("```json")) {
                cleanedResult = cleanedResult.substring(7).trim();
              }
              if (cleanedResult.endsWith("```")) {
                cleanedResult =
                    cleanedResult.substring(0, cleanedResult.length - 3).trim();
              }

              if (!cleanedResult.startsWith('[') ||
                  !cleanedResult.endsWith(']')) {
                throw FormatException(
                    "LLM 未返回有效的 JSON 数组 (未找到 '[' 或 ']'). Raw:\n$cleanedResult");
              }

              decodedResult = jsonDecode(cleanedResult);
              print(
                  "[DEBUG] 批次 ${i + 1} decodedResult Type: ${decodedResult.runtimeType}");
            } on FormatException catch (e) {
              print(
                  "[LightweightGenerationService.generateOutline] 错误: 批次 ${i + 1} (尝试 ${retryCount + 1}) JSON 解码失败. 错误: $e.");

              // 尝试使用AI修复格式错误
              onRealtimeOutput?.call("⚠️ JSON格式错误，尝试AI自动修复...\n");
              try {
                final fixedResult =
                    await _fixJsonFormatWithAI(rawResult, 'outline');
                if (fixedResult != null) {
                  onRealtimeOutput?.call("✅ AI修复成功，重新解析...\n");
                  decodedResult = jsonDecode(fixedResult);
                  print("[LightweightGenerationService] AI修复JSON成功");
                } else {
                  onRealtimeOutput?.call("❌ AI修复失败，将重试生成...\n");
                  rethrow;
                }
              } catch (fixError) {
                print("[LightweightGenerationService] AI修复JSON失败: $fixError");
                onRealtimeOutput?.call("❌ AI修复失败: $fixError\n");
                rethrow;
              }
            }

            if (decodedResult is List) {
              int chaptersInBatch = 0;
              List<Map<String, dynamic>> batchChapters = [];
              for (var item in decodedResult) {
                if (item is Map<String, dynamic> &&
                    item.containsKey('chapterNumber') &&
                    item['chapterNumber'] is int &&
                    item.containsKey('chapterTitle') &&
                    item['chapterTitle'] is String &&
                    item.containsKey('summary') &&
                    item['summary'] is String &&
                    item['chapterNumber'] >= startChapter &&
                    item['chapterNumber'] <= endChapter) {
                  batchChapters.add(item);
                  chaptersInBatch++;
                } else {
                  print(
                      "[LightweightGenerationService.generateOutline] 警告: 批次 ${i + 1} 中发现无效或超出范围的章节结构: $item. 跳过.");
                }
              }

              if (chaptersInBatch != (endChapter - startChapter + 1)) {
                print(
                    "[LightweightGenerationService.generateOutline] 警告: 批次 ${i + 1} 解析出的章节数 ($chaptersInBatch) 与预期 (${endChapter - startChapter + 1}) 不符.");
              }

              allChapters.addAll(batchChapters);
              print(
                  "[LightweightGenerationService.generateOutline] 批次 ${i + 1} (尝试 ${retryCount + 1}) 成功解析. 添加了 $chaptersInBatch 个章节.");

              // 实时输出批次成功信息
              onRealtimeOutput
                  ?.call("✅ 批次 ${i + 1} 成功！解析了 $chaptersInBatch 个章节\n");

              // langchain的memory机制会自动保存上下文，无需手动管理

              batchSuccess = true;
            } else {
              throw FormatException(
                  "解码结果不是 List 类型. 类型: ${decodedResult.runtimeType}");
            }
          } catch (e) {
            print(
                "[LightweightGenerationService.generateOutline] 错误: 处理批次 ${i + 1} (尝试 ${retryCount + 1}) 失败: $e");

            // 实时输出错误信息
            onRealtimeOutput
                ?.call("❌ 批次 ${i + 1} 第${retryCount + 1}次尝试失败: $e\n");

            retryCount++;
            if (retryCount > maxRetries) {
              print(
                  "[LightweightGenerationService.generateOutline] 错误: 批次 ${i + 1} 达到最大重试次数 ($maxRetries). 大纲生成失败.");

              // 实时输出最终失败信息
              onRealtimeOutput?.call("💥 批次 ${i + 1} 达到最大重试次数，生成失败！\n");

              throw Exception("处理大纲批次 ${i + 1} 失败，已达到最大重试次数.");
            } else {
              print(
                  "[LightweightGenerationService.generateOutline] 在 ${3 * retryCount} 秒后重试批次 ${i + 1}...");

              // 实时输出重试等待信息
              onRealtimeOutput
                  ?.call("⏳ 等待 ${3 * retryCount} 秒后重试批次 ${i + 1}...\n");

              await Future.delayed(Duration(seconds: 3 * retryCount));
            }
          }
        }
      }

      // 聚合和保存
      onRealtimeOutput?.call("\n🎉 所有批次处理完成！\n");
      onRealtimeOutput?.call("📊 统计信息：\n");
      onRealtimeOutput?.call("  - 请求章节数：$totalChapters\n");
      onRealtimeOutput?.call("  - 实际生成数：${allChapters.length}\n");

      if (allChapters.length != totalChapters) {
        print(
            "[LightweightGenerationService.generateOutline] 警告: 最终章节计数 (${allChapters.length}) 与请求的总数 ($totalChapters) 不符.");
        onRealtimeOutput?.call("⚠️ 警告：生成的章节数与请求数不符！\n");
      } else {
        onRealtimeOutput?.call("✅ 章节数量完全匹配！\n");
      }

      allChapters.sort((a, b) =>
          (a['chapterNumber'] as int).compareTo(b['chapterNumber'] as int));

      final finalOutlineJson = {
        'novelTitle': novelTitle,
        'chapters': allChapters,
        'generatedAt': DateTime.now().toIso8601String(),
        'totalChaptersGenerated': allChapters.length,
        'totalChaptersRequested': totalChapters,
      };

      String finalJsonString;
      try {
        finalJsonString = jsonEncode(finalOutlineJson);
        print(
            "[LightweightGenerationService.generateOutline] 最终聚合的大纲 JSON 已生成 (长度: ${finalJsonString.length}).");
      } catch (e) {
        print(
            "[LightweightGenerationService.generateOutline] 错误: 无法编码最终 JSON 大纲: $e");
        throw Exception("无法创建最终的大纲 JSON 结构.");
      }

      // 保存完整JSON大纲到NovelMemory
      try {
        await novelMemory.saveOutline(finalJsonString);
        print(
            "[LightweightGenerationService.generateOutline] 完整 JSON 大纲已保存到 NovelMemory ('$novelTitle').");
      } catch (e) {
        print(
            "[LightweightGenerationService.generateOutline] 错误: 无法将最终 JSON 大纲保存到 NovelMemory: $e");
        throw Exception("无法保存生成的大纲.");
      }

      // 保存大纲生成记录到对话历史
      final userMessage = app_chat.ChatMessage.user(
        content: "生成《$novelTitle》的大纲，共$totalChapters章",
        novelTitle: novelTitle,
      );
      await _chatHistoryService.addMessage(userMessage);

      // 生成简单的大纲摘要作为AI回复
      final chapters = finalOutlineJson['chapters'] as List<dynamic>? ?? [];
      final summaryBuffer =
          StringBuffer("已生成《$novelTitle》的大纲，共${chapters.length}章:\n");
      for (var chapter in chapters) {
        if (chapter is Map<String, dynamic>) {
          final chapterNumber = chapter['chapterNumber'];
          final chapterTitle = chapter['chapterTitle'];
          summaryBuffer.writeln("- 第$chapterNumber章：$chapterTitle");
        }
      }

      final aiMessage = app_chat.ChatMessage.ai(
        content: summaryBuffer.toString(),
        novelTitle: novelTitle,
      );
      await _chatHistoryService.addMessage(aiMessage);

      // 返回纯章节JSON数组字符串
      try {
        final List<dynamic> chaptersList =
            finalOutlineJson['chapters'] as List<dynamic>? ?? [];
        final String chaptersJsonArrayString = jsonEncode(chaptersList);
        print(
            "[LightweightGenerationService.generateOutline] 返回纯章节 JSON 数组字符串 (长度: ${chaptersJsonArrayString.length}).");

        // 实时输出最终完成信息
        onRealtimeOutput?.call("\n🎉 大纲生成完全完成！\n");
        onRealtimeOutput?.call("📋 最终结果：\n");
        onRealtimeOutput?.call("  - 成功生成 ${chaptersList.length} 个章节\n");
        onRealtimeOutput
            ?.call("  - JSON数据长度：${chaptersJsonArrayString.length} 字符\n");
        onRealtimeOutput?.call("✅ 大纲已保存到内存和对话历史\n");

        return chaptersJsonArrayString;
      } catch (e) {
        print(
            "[LightweightGenerationService.generateOutline] 错误: 无法编码纯章节 JSON 数组: $e");
        onRealtimeOutput?.call("❌ 无法编码章节JSON数组: $e\n");
        throw Exception("无法为 UI 准备章节 JSON 数组.");
      }
    } catch (e) {
      print('[LightweightGenerationService.generateOutline] 生成大纲失败: $e');
      onRealtimeOutput?.call("❌ 大纲生成失败: $e\n");
      rethrow;
    }
  }

  /// 生成详细章节大纲
  /// 与标准生成服务相同，因为细纲生成是必要的
  Future<String> generateDetailedChapterOutline({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String chapterSummary,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    print(
        "[LightweightGenerationService.generateDetailedChapterOutline] 开始为 '$novelTitle' 第 $chapterNumber 章生成详细大纲...");
    try {
      final detailedChain = await _getDetailedChain(novelTitle);
      final novelMemory = _getNovelMemory(novelTitle, sessionId: sessionId);

      // 获取存储的完整JSON大纲
      Map<String, dynamic> fullOutline;
      try {
        final outlineJsonString = await novelMemory.getOutline();
        if (outlineJsonString == null || outlineJsonString.isEmpty) {
          throw Exception("未找到或为空: '$novelTitle' 的已存储大纲.");
        }
        fullOutline = jsonDecode(outlineJsonString) as Map<String, dynamic>;
      } catch (e) {
        print(
            "[LightweightGenerationService.generateDetailedChapterOutline] 错误: 检索/解析已存储大纲失败: $e");
        throw Exception("无法加载或解析基础小说大纲. 请先生成主要大纲.");
      }

      // 准备输入参数
      String charactersString =
          _buildCharacterDetailsString(characterCards ?? {});
      String writingStylePromptString = writingStyle?.getPrompt() ?? '';
      String knowledgeBaseString = '';

      final knowledgeBaseController = Get.find<KnowledgeBaseController>();
      if (knowledgeBaseController.useKnowledgeBase.value &&
          knowledgeBaseController.selectedDocIds.isNotEmpty) {
        knowledgeBaseString = knowledgeBaseController.getSelectedDocsContent();
      }

      // 获取前一章和后一章的详细信息（优先使用已生成的详细细纲）
      String previousChapterInfo = '';
      String nextChapterInfo = '';

      // 首先尝试从已生成的详细细纲中获取信息
      final allDetailedChapters = await novelMemory.getAllChapters();

      // 获取前一章的详细细纲
      if (chapterNumber > 1 && allDetailedChapters.containsKey(chapterNumber - 1)) {
        final prevDetailedOutline = allDetailedChapters[chapterNumber - 1]!;
        final prevTitle = await novelMemory.getChapterTitle(chapterNumber - 1) ?? "第${chapterNumber - 1}章";
        previousChapterInfo = "前一章详细细纲: $prevTitle\n$prevDetailedOutline";
      } else if (fullOutline.containsKey('chapters') && fullOutline['chapters'] is List) {
        // 如果没有详细细纲，则使用基础大纲
        final chapters = fullOutline['chapters'] as List;
        if (chapterNumber > 1 && chapters.length >= chapterNumber - 1) {
          final prevChapter = chapters[chapterNumber - 2];
          if (prevChapter is Map &&
              prevChapter.containsKey('title') &&
              prevChapter.containsKey('summary')) {
            previousChapterInfo =
                "前一章基础大纲: ${prevChapter['title']}\n${prevChapter['summary']}";
          }
        }
      }

      // 获取后一章的基础信息（通常后续章节还没有详细细纲）
      if (fullOutline.containsKey('chapters') && fullOutline['chapters'] is List) {
        final chapters = fullOutline['chapters'] as List;
        if (chapters.length > chapterNumber) {
          final nextChapter = chapters[chapterNumber];
          if (nextChapter is Map &&
              nextChapter.containsKey('title') &&
              nextChapter.containsKey('summary')) {
            nextChapterInfo =
                "后一章基础大纲: ${nextChapter['title']}\n${nextChapter['summary']}";
          }
        }
      }

      // 将已有的细纲信息添加到 Memory 中
      await _addExistingDetailedOutlinesToMemory(
          detailedChain, novelTitle, chapterNumber, sessionId);

      final input = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'theme': theme,
        'targetReaders': targetReaders,
        'chapterNumber': chapterNumber.toString(),
        'chapterTitle': chapterTitle,
        'chapterSummary': chapterSummary,
        'previousChapterInfo': previousChapterInfo,
        'nextChapterInfo': nextChapterInfo,
        'background': background ?? '',
        'otherRequirements': otherRequirements ?? '',
        'characters': charactersString,
        'writingStylePrompt': writingStylePromptString,
        'knowledgeBase': knowledgeBaseString,
        'task': 'generate_detailed_outline',
        'input': "为《$novelTitle》第$chapterNumber章《$chapterTitle》生成详细大纲",
      };

      // 执行生成
      String response;

      // 无论是否有onProgress回调，都使用流式模式，这样可以确保阿里云通义千问模型正常工作
      print(
          "[LightweightGenerationService.generateDetailedChapterOutline] 使用流式模式生成细纲...");
      final buffer = StringBuffer();

      try {
        // 使用DetailedOutlineChain的generateDetailedOutlineForChapter方法
        // 这个方法现在使用标准的LLMChain.run()，包含Memory支持
        if (onProgress != null) {
          // 如果需要进度回调，使用流式方法
          final resultStream = detailedChain.stream(input);
          await for (final chunk in resultStream) {
            final String textChunk = chunk.toString();
            buffer.write(textChunk);
            onProgress(textChunk);
          }
          response = buffer.toString();
        } else {
          // 否则使用标准方法
          response = await detailedChain.generateDetailedOutlineForChapter(input);
        }
      } catch (e) {
        print(
            "[LightweightGenerationService.generateDetailedChapterOutline] 生成细纲失败: $e");
        rethrow;
      }

      // 检查细纲内容是否合理，如果有明显的格式问题，尝试AI修复
      if (response.trim().isEmpty ||
          response.contains('```') ||
          response.contains('json')) {
        onProgress?.call("⚠️ 检测到细纲格式问题，尝试AI自动修复...\n");
        try {
          final fixedResponse =
              await _fixJsonFormatWithAI(response, 'detailed_outline');
          if (fixedResponse != null && fixedResponse.trim().isNotEmpty) {
            onProgress?.call("✅ AI修复成功\n");
            response = fixedResponse;
            print("[LightweightGenerationService] 细纲AI修复成功");
          } else {
            onProgress?.call("⚠️ AI修复未改善内容，使用原始结果\n");
          }
        } catch (fixError) {
          print("[LightweightGenerationService] 细纲AI修复失败: $fixError");
          onProgress?.call("⚠️ AI修复失败，使用原始结果\n");
        }
      }

      // 保存详细大纲到NovelMemory
      try {
        print(
            "[LightweightGenerationService.generateDetailedChapterOutline] 保存第 $chapterNumber 章细纲到NovelMemory");
        await novelMemory.saveChapter(chapterNumber, chapterTitle, response);
        print(
            "[LightweightGenerationService.generateDetailedChapterOutline] 细纲保存成功");

        // 验证保存是否成功
        final savedContent = await novelMemory.getChapter(chapterNumber);
        if (savedContent != null && savedContent.isNotEmpty) {
          print("[LightweightGenerationService.generateDetailedChapterOutline] 细纲保存验证成功");
        } else {
          print("[LightweightGenerationService.generateDetailedChapterOutline] 警告：细纲保存验证失败");
        }
      } catch (e) {
        print(
            "[LightweightGenerationService.generateDetailedChapterOutline] 保存细纲到NovelMemory失败: $e");
        // 不中断执行
      }

      // 保存详细大纲生成记录到对话历史
      final userMessage = app_chat.ChatMessage.user(
        content: "为《$novelTitle》第$chapterNumber章《$chapterTitle》生成详细大纲",
        novelTitle: novelTitle,
      );

      final aiMessage = app_chat.ChatMessage.ai(
        content: response,
        novelTitle: novelTitle,
      );

      await _chatHistoryService.addMessage(userMessage);
      await _chatHistoryService.addMessage(aiMessage);

      return response;
    } catch (e) {
      print(
          '[LightweightGenerationService.generateDetailedChapterOutline] 生成详细章节大纲失败: $e');
      rethrow;
    }
  }

  /// 生成章节内容
  /// 精简版本 - 不保存章节内容到NovelMemory，只保存细纲的记忆
  Future<String> generateChapter({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String outlineContent,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    Map<String, CharacterCard>? characterCards,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    print(
        "[LightweightGenerationService.generateChapter] 开始为 '$novelTitle' 第 $chapterNumber 章生成内容 (精简模式)...");
    try {
      // 获取当前模型配置
      final modelConfig = _apiConfigController.getCurrentModel();

      // 准备输入参数
      String charactersString =
          _buildCharacterDetailsString(characterCards ?? {});
      // 不再直接获取文风包和知识库内容，而是通过嵌入模型检索相关内容
      String writingStylePromptString = '';
      String knowledgeBaseString = '';

      // 获取大纲和细纲信息
      final novelMemory = _getNovelMemory(novelTitle, sessionId: sessionId);
      String fullNovelContext = '';

      try {
        // 获取大纲信息
        final outlineJson = await novelMemory.getOutline();
        if (outlineJson != null && outlineJson.isNotEmpty) {
          print(
              "[LightweightGenerationService.generateChapter] 成功获取小说大纲，长度: ${outlineJson.length}");
          fullNovelContext = "# 小说大纲\n$outlineJson\n\n";
        }

        // 获取所有章节的细纲信息（包括前面章节、当前章节和后续章节）
        Map<int, String> detailedOutlines = {};

        // 获取大纲中的总章节数
        int totalChapters = 0;
        try {
          if (outlineJson != null && outlineJson.isNotEmpty) {
            final outlineJsonObj =
                jsonDecode(outlineJson) as Map<String, dynamic>;
            if (outlineJsonObj.containsKey('chapters') &&
                outlineJsonObj['chapters'] is List) {
              totalChapters = (outlineJsonObj['chapters'] as List).length;
            }
          }
        } catch (e) {
          // 出错时记录日志但不中断执行
          // 使用默认值
        }

        // 如果无法从大纲获取总章节数，使用默认值
        if (totalChapters == 0) {
          totalChapters = chapterNumber + 5; // 假设至少还有5章
        }

        // 获取所有可用的章节细纲（每次都传递所有细纲）
        // 1. 获取所有前面章节的细纲（已生成的章节）
        for (int i = 1; i < chapterNumber; i++) {
          final detailedOutline = await novelMemory.getChapter(i);
          if (detailedOutline != null) {
            detailedOutlines[i] = detailedOutline;
          }
        }

        // 2. 获取当前章节的细纲（如果有）
        final currentChapterOutline =
            await novelMemory.getChapter(chapterNumber);
        if (currentChapterOutline != null) {
          detailedOutlines[chapterNumber] = currentChapterOutline;
        }

        // 3. 获取所有后续章节的细纲（不限制数量，确保传递所有细纲）
        for (int i = chapterNumber + 1; i <= totalChapters; i++) {
          final futureChapterOutline = await novelMemory.getChapter(i);
          if (futureChapterOutline != null) {
            detailedOutlines[i] = futureChapterOutline;
          }
        }

        print(
            "[LightweightGenerationService] 已获取细纲数量: ${detailedOutlines.length}/${totalChapters}");

        if (detailedOutlines.isNotEmpty) {
          fullNovelContext += "# 所有章节细纲\n";

          // 按章节号排序
          final sortedChapterNumbers = detailedOutlines.keys.toList()..sort();

          for (final chapterNum in sortedChapterNumbers) {
            final content = detailedOutlines[chapterNum]!;

            // 标记当前章节
            if (chapterNum == chapterNumber) {
              fullNovelContext += "## 第$chapterNum章细纲（当前章节）\n$content\n\n";
            }
            // 标记前面章节
            else if (chapterNum < chapterNumber) {
              fullNovelContext += "## 第$chapterNum章细纲（已生成）\n$content\n\n";
            }
            // 标记后续章节
            else {
              fullNovelContext += "## 第$chapterNum章细纲（后续章节）\n$content\n\n";
            }
          }
        }

        // 使用嵌入模型获取相关内容
        if (_apiConfigController.embeddingModel.value.enabled) {
          try {
            print(
                "[LightweightGenerationService.generateChapter] 嵌入模型已启用，尝试获取相关内容");

            // 检查小说是否已向量化
            if (!_vectorizationService.isNovelVectorized(novelTitle)) {
              print(
                  "[LightweightGenerationService.generateChapter] 小说未向量化，开始向量化...");
              await _vectorizationService.vectorizeNovel(novelTitle);
            }

            // 构建优化的查询文本，包含更多上下文信息
            final queryText = _buildOptimizedQueryText(
              novelTitle: novelTitle,
              chapterNumber: chapterNumber,
              chapterTitle: chapterTitle,
              outlineContent: outlineContent,
              theme: theme,
              genres: genres,
            );

            // 获取相关内容，包括章节、知识库和文风包
            final searchResults =
                await _vectorizationService.searchNovelContent(
              novelTitle,
              queryText,
              maxResults: _apiConfigController.embeddingModel.value.topK,
              includeKnowledgeBase: true, // 包含知识库内容
              includeWritingStyle: true, // 包含文风包内容
            );

            if (searchResults.isNotEmpty) {
              fullNovelContext += "# 相关内容\n";
              for (final result in searchResults) {
                final content = result['content'] as String;
                final title = result['title'] as String;
                // final chapter = result['chapter'] as int; // 暂时不使用章节号
                final similarity = result['similarity'] as double;

                // 只添加相似度高于阈值的内容
                if (similarity >=
                    _apiConfigController
                        .embeddingModel.value.similarityThreshold) {
                  fullNovelContext +=
                      "## $title (相似度: ${(similarity * 100).toStringAsFixed(1)}%)\n$content\n\n";
                }
              }
            }
          } catch (e) {
            print(
                "[LightweightGenerationService.generateChapter] 获取相关内容失败: $e");
            // 出错时不中断执行
          }
        }
      } catch (e) {
        print("[LightweightGenerationService.generateChapter] 获取小说上下文时出错: $e");
        // 出错时不中断执行，使用空字符串
        fullNovelContext = '';
      }

      // 使用 LangChain Memory 机制传递细纲信息

      // 准备章节生成的输入参数（不再包含 history，改用 memory）
      final input = {
        'novelTitle': novelTitle,
        'genres': genres.join(', '),
        'chapterNumber': chapterNumber.toString(),
        'chapterTitle': chapterTitle,
        'outlineContent': outlineContent,
        'task': 'generate_chapter',
        'theme': theme,
        'targetReaders': targetReaders,
        'background': background ?? '无背景',
        'otherRequirements': otherRequirements ?? '无其他要求',
        'knowledgeBase':
            knowledgeBaseString.isNotEmpty ? knowledgeBaseString : '无知识库信息',
        'characters': charactersString,
        'writingStylePrompt': writingStylePromptString,
        'input': "生成第$chapterNumber章：$chapterTitle",
      };

      // 创建带有 Memory 的生成链
      final llm = ModelAdapter.createLLMFromConfig(modelConfig);
      final tempChain = NovelGenerationChain(llm: llm, novelTitle: novelTitle);

      // 将所有细纲信息添加到 Memory 中
      await _addOutlinesToMemory(tempChain, fullNovelContext);

      // 执行生成
      String response;
      if (onProgress != null) {
        final resultStream = tempChain.stream(input);
        final buffer = StringBuffer();
        await for (final chunk in resultStream) {
          final String textChunk = chunk.toString();
          buffer.write(textChunk);
          onProgress(textChunk);
        }
        response = buffer.toString();
      } else {
        response = await tempChain.run(input);
      }

      // 检查章节内容是否合理，如果有明显的格式问题，尝试AI修复
      if (response.trim().isEmpty ||
          response.contains('```') ||
          response.contains('json') ||
          response.contains('JSON') ||
          response.length < 500) {
        // 章节内容太短可能有问题
        onProgress?.call("⚠️ 检测到章节内容格式问题，尝试AI自动修复...\n");
        try {
          final fixedResponse =
              await _fixJsonFormatWithAI(response, 'chapter_content');
          if (fixedResponse != null &&
              fixedResponse.trim().isNotEmpty &&
              fixedResponse.length > response.length) {
            onProgress?.call("✅ AI修复成功，内容已优化\n");
            response = fixedResponse;
            print("[LightweightGenerationService] 章节内容AI修复成功");
          } else {
            onProgress?.call("⚠️ AI修复未改善内容，使用原始结果\n");
          }
        } catch (fixError) {
          print("[LightweightGenerationService] 章节内容AI修复失败: $fixError");
          onProgress?.call("⚠️ AI修复失败，使用原始结果\n");
        }
      }

      // 不再进行连贯性检查，因为已经有所有章节的大纲作为上下文

      // 保存章节生成记录到对话历史
      final userMessage = app_chat.ChatMessage.user(
        content: "生成《$novelTitle》第$chapterNumber章《$chapterTitle》",
        novelTitle: novelTitle,
      );

      final aiMessage = app_chat.ChatMessage.ai(
        content: response,
        novelTitle: novelTitle,
      );

      await _chatHistoryService.addMessage(userMessage);
      await _chatHistoryService.addMessage(aiMessage);

      // 如果启用了嵌入模型，将新生成的章节内容向量化并添加到嵌入模型的参考内容中
      if (_apiConfigController.embeddingModel.value.enabled) {
        try {
          print(
              "[LightweightGenerationService.generateChapter] 尝试向量化新生成的章节内容...");
          await _vectorizationService.vectorizeChapter(
            novelTitle: novelTitle,
            chapterNumber: chapterNumber,
            chapterTitle: chapterTitle,
            content: response,
          );
          print("[LightweightGenerationService.generateChapter] 章节内容向量化成功");
        } catch (e) {
          print("[LightweightGenerationService.generateChapter] 章节内容向量化失败: $e");
          // 出错时不中断执行
        }
      }

      return response;
    } catch (e) {
      print('[LightweightGenerationService.generateChapter] 生成章节内容失败: $e');
      rethrow;
    }
  }

  // 连贯性检查功能已移除，因为已经有所有章节的大纲作为上下文

  /// 构建优化的查询文本，用于嵌入模型检索
  ///
  /// 通过包含更多上下文信息，特别强调前文故事情节和人物发展，提高检索的准确性和相关性
  String _buildOptimizedQueryText({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String outlineContent,
    required String theme,
    required List<String> genres,
  }) {
    final buffer = StringBuffer();

    // 1. 基础章节信息
    buffer.write('小说《$novelTitle》第$chapterNumber章《$chapterTitle》');

    // 2. 添加类型和主题信息，帮助检索相关风格的内容
    if (genres.isNotEmpty) {
      buffer.write(' 类型：${genres.join('、')}');
    }
    if (theme.isNotEmpty) {
      buffer.write(' 主题：$theme');
    }

    // 3. 添加章节大纲内容
    buffer.write(' 章节内容：$outlineContent');

    // 4. 强调前文故事情节和人物发展的检索需求
    buffer.write(' 重点检索前文内容：');

    // 4.1 前文故事情节相关
    buffer.write('已发生的关键事件、情节转折点、未解决的冲突、');
    buffer.write('故事线索发展、悬念铺垫、伏笔设置、');

    // 4.2 人物发展相关
    buffer.write('主要角色的成长轨迹、性格变化历程、');
    buffer.write('人物关系演变、角色动机转变、');
    buffer.write('人物间的互动历史、情感发展脉络、');

    // 4.3 连贯性相关
    buffer.write('角色当前状态、未完成的对话、');
    buffer.write('待解决的问题、情感延续、行为模式');

    // 5. 根据章节号添加特定的检索重点
    if (chapterNumber == 1) {
      buffer.write(' 第一章重点：世界观设定、主角初始状态、开篇氛围营造');
    } else if (chapterNumber <= 3) {
      buffer.write(' 前期章节重点：');
      buffer.write('前文人物介绍细节、已建立的人物关系、');
      buffer.write('初期情节发展、角色初始动机、环境背景延续');
    } else if (chapterNumber <= 10) {
      buffer.write(' 中期章节重点：');
      buffer.write('前文重要情节节点、人物关系发展历程、');
      buffer.write('已出现的冲突线索、角色成长变化、');
      buffer.write('重要对话和承诺、情感发展轨迹');
    } else {
      buffer.write(' 后期章节重点：');
      buffer.write('前文所有关键情节、人物完整发展历程、');
      buffer.write('所有伏笔和悬念、人物关系最终状态、');
      buffer.write('需要呼应的重要细节、情感高潮铺垫');
    }

    // 6. 根据章节大纲内容添加智能化的检索重点
    final intelligentFocus = _analyzeOutlineForQueryFocus(outlineContent);
    if (intelligentFocus.isNotEmpty) {
      buffer.write(' 智能检索重点：$intelligentFocus');
    }

    // 7. 添加具体的检索目标
    buffer.write(' 检索目标：确保新章节与前文在情节逻辑、人物行为、情感状态上完全连贯');

    return buffer.toString();
  }

  /// 分析章节大纲内容，提取智能化的检索重点
  ///
  /// 根据大纲中提到的人物、事件、情感等关键词，生成针对性的检索重点
  String _analyzeOutlineForQueryFocus(String outlineContent) {
    final lowerOutline = outlineContent.toLowerCase();

    // 检测人物相关关键词
    final characterKeywords = [
      '主角',
      '男主',
      '女主',
      '主人公',
      '角色',
      '人物',
      '他',
      '她',
      '某某'
    ];
    final hasCharacterFocus =
        characterKeywords.any((keyword) => lowerOutline.contains(keyword));

    // 检测情感相关关键词
    final emotionKeywords = [
      '爱情',
      '友情',
      '亲情',
      '愤怒',
      '悲伤',
      '喜悦',
      '恐惧',
      '紧张',
      '激动',
      '失望',
      '希望',
      '绝望'
    ];
    final hasEmotionFocus =
        emotionKeywords.any((keyword) => lowerOutline.contains(keyword));

    // 检测冲突相关关键词
    final conflictKeywords = [
      '冲突',
      '矛盾',
      '对抗',
      '战斗',
      '争执',
      '分歧',
      '危机',
      '困难',
      '挑战',
      '阻碍'
    ];
    final hasConflictFocus =
        conflictKeywords.any((keyword) => lowerOutline.contains(keyword));

    // 检测回忆/过往相关关键词
    final memoryKeywords = ['回忆', '过去', '以前', '曾经', '往事', '历史', '从前', '当初'];
    final hasMemoryFocus =
        memoryKeywords.any((keyword) => lowerOutline.contains(keyword));

    // 检测对话相关关键词
    final dialogueKeywords = [
      '对话',
      '交谈',
      '说话',
      '告诉',
      '询问',
      '回答',
      '解释',
      '承认',
      '坦白'
    ];
    final hasDialogueFocus =
        dialogueKeywords.any((keyword) => lowerOutline.contains(keyword));

    // 检测决定/选择相关关键词
    final decisionKeywords = ['决定', '选择', '决心', '下定决心', '考虑', '思考', '权衡', '判断'];
    final hasDecisionFocus =
        decisionKeywords.any((keyword) => lowerOutline.contains(keyword));

    // 根据检测结果生成针对性的检索重点
    final focusPoints = <String>[];

    if (hasCharacterFocus) {
      focusPoints.add('相关人物的前文出场情况、性格特征、行为模式');
    }

    if (hasEmotionFocus) {
      focusPoints.add('相关角色的情感发展历程、情感状态变化');
    }

    if (hasConflictFocus) {
      focusPoints.add('前文冲突的起因和发展、相关角色的立场和态度');
    }

    if (hasMemoryFocus) {
      focusPoints.add('相关的历史事件、过往经历、重要回忆片段');
    }

    if (hasDialogueFocus) {
      focusPoints.add('相关角色的对话风格、重要对话内容、未完成的交流');
    }

    if (hasDecisionFocus) {
      focusPoints.add('相关角色的思考过程、价值观念、决策依据');
    }

    // 检测具体人物名称（简单的启发式检测）
    final possibleNames = _extractPossibleCharacterNames(outlineContent);
    if (possibleNames.isNotEmpty) {
      focusPoints.add('人物${possibleNames.join('、')}的前文相关内容');
    }

    return focusPoints.join('、');
  }

  /// 从大纲中提取可能的人物名称
  ///
  /// 使用简单的启发式方法检测可能的人物名称
  List<String> _extractPossibleCharacterNames(String outlineContent) {
    final names = <String>[];

    // 检测常见的中文姓名模式（2-4个字符，包含常见姓氏）
    final commonSurnames = [
      '李',
      '王',
      '张',
      '刘',
      '陈',
      '杨',
      '赵',
      '黄',
      '周',
      '吴',
      '徐',
      '孙',
      '胡',
      '朱',
      '高',
      '林',
      '何',
      '郭',
      '马',
      '罗'
    ];

    // 简单的正则匹配（这里使用基础的字符串处理）
    final words = outlineContent.split(RegExp(r'[，。！？；：\s]+'));

    for (final word in words) {
      if (word.length >= 2 && word.length <= 4) {
        // 检查是否以常见姓氏开头
        if (commonSurnames.any((surname) => word.startsWith(surname))) {
          if (!names.contains(word)) {
            names.add(word);
          }
        }
      }
    }

    // 限制返回的名称数量，避免查询过长
    return names.take(3).toList();
  }

  /// 将所有细纲信息添加到 LangChain Memory 中
  ///
  /// 通过 Memory 机制隐性传递细纲信息，而不是通过 history 参数显式传递
  Future<void> _addOutlinesToMemory(
      NovelGenerationChain chain, String outlineContext) async {
    try {
      print("[LightweightGenerationService] 开始将细纲信息添加到 LangChain Memory...");

      if (outlineContext.isEmpty) {
        print("[LightweightGenerationService] 细纲上下文为空，跳过添加到 Memory");
        return;
      }

      // 将细纲信息作为系统上下文添加到 Memory 中
      // 使用特殊的输入和输出格式，确保细纲信息被正确存储
      await chain.memory.saveContext(
        inputValues: {
          chain.memory.inputKey!: "系统：加载小说细纲和上下文信息",
        },
        outputValues: {
          chain.memory.outputKey!: outlineContext,
        },
      );

      print(
          "[LightweightGenerationService] 细纲信息已成功添加到 LangChain Memory，长度: ${outlineContext.length}");

      // 验证 Memory 中的内容
      final memoryVariables = await chain.memory.loadMemoryVariables({
        chain.memory.inputKey!: "验证",
      });

      if (memoryVariables.containsKey('history')) {
        final historyLength = memoryVariables['history'].toString().length;
        print(
            "[LightweightGenerationService] Memory 验证成功，当前历史记录长度: $historyLength");
      }
    } catch (e) {
      print("[LightweightGenerationService] 添加细纲信息到 Memory 失败: $e");
      // 不抛出异常，允许继续执行
    }
  }

  /// 将已有大纲信息添加到 Memory 中（用于续写模式）
  ///
  /// 在续写模式下，将已生成的大纲信息添加到 Memory 中，确保新生成的内容与已有内容保持一致
  Future<void> _addExistingOutlineToMemory(
      NovelGenerationChain chain, String novelTitle, String? sessionId) async {
    try {
      print("[LightweightGenerationService] 开始将已有大纲信息添加到 Memory（续写模式）...");

      final novelMemory = _getNovelMemory(novelTitle, sessionId: sessionId);

      // 获取已有的大纲信息
      final existingOutline = await novelMemory.getOutline();
      if (existingOutline != null && existingOutline.isNotEmpty) {
        // 将已有大纲作为上下文添加到 Memory 中
        await chain.memory.saveContext(
          inputValues: {
            chain.memory.inputKey!: "系统：加载已有小说大纲（续写模式）",
          },
          outputValues: {
            chain.memory.outputKey!: "已有大纲信息：\n$existingOutline",
          },
        );

        print(
            "[LightweightGenerationService] 已有大纲信息已添加到 Memory，长度: ${existingOutline.length}");
      }

      // 获取已有的章节细纲信息
      final allChapters = await novelMemory.getAllChapters();
      if (allChapters.isNotEmpty) {
        final outlineBuffer = StringBuffer();
        outlineBuffer.writeln("已有章节细纲：");

        // 按章节号排序
        final sortedChapterNumbers = allChapters.keys.toList()..sort();

        for (final chapterNum in sortedChapterNumbers) {
          final content = allChapters[chapterNum]!;
          outlineBuffer.writeln("## 第$chapterNum章细纲");
          outlineBuffer.writeln(content);
          outlineBuffer.writeln();
        }

        // 将已有细纲信息添加到 Memory 中
        await chain.memory.saveContext(
          inputValues: {
            chain.memory.inputKey!: "系统：加载已有章节细纲（续写模式）",
          },
          outputValues: {
            chain.memory.outputKey!: outlineBuffer.toString(),
          },
        );

        print(
            "[LightweightGenerationService] 已有细纲信息已添加到 Memory，章节数: ${allChapters.length}");
      }
    } catch (e) {
      print("[LightweightGenerationService] 添加已有大纲信息到 Memory 失败: $e");
      // 不抛出异常，允许继续执行
    }
  }

  /// 将已有的详细细纲信息添加到 Memory 中（用于细纲生成）
  ///
  /// 在生成新的细纲时，将已生成的其他章节细纲添加到 Memory 中，确保新细纲与已有细纲保持连贯性
  Future<void> _addExistingDetailedOutlinesToMemory(dynamic detailedChain,
      String novelTitle, int currentChapterNumber, String? sessionId) async {
    try {
      print("[LightweightGenerationService] 开始将已有细纲信息添加到 Memory（细纲生成模式）...");

      final novelMemory = _getNovelMemory(novelTitle, sessionId: sessionId);

      // 获取所有已生成的章节细纲
      final allChapters = await novelMemory.getAllChapters();
      if (allChapters.isEmpty) {
        print("[LightweightGenerationService] 没有已生成的细纲，跳过添加到 Memory");
        return;
      }

      // 构建已有细纲的上下文信息
      final outlineBuffer = StringBuffer();
      outlineBuffer.writeln("=== 已生成的章节细纲和关键事件时间线 ===");
      outlineBuffer.writeln("**重要提醒：严格禁止重复以下已发生的情节和事件！**");
      outlineBuffer.writeln();

      // 按章节号排序，排除当前正在生成的章节
      final sortedChapterNumbers = allChapters.keys
          .where((chapterNum) => chapterNum != currentChapterNumber)
          .toList()
        ..sort();

      if (sortedChapterNumbers.isEmpty) {
        print("[LightweightGenerationService] 没有其他章节的细纲，跳过添加到 Memory");
        return;
      }

      // 构建关键事件时间线
      outlineBuffer.writeln("### 关键事件时间线（已发生）：");
      for (final chapterNum in sortedChapterNumbers.where((num) => num < currentChapterNumber)) {
        final chapterTitle = await novelMemory.getChapterTitle(chapterNum) ?? "第${chapterNum}章";
        final content = allChapters[chapterNum]!;

        // 提取关键事件（简化版，提取章节摘要部分）
        final lines = content.split('\n');
        String summary = '';
        for (int i = 0; i < lines.length; i++) {
          if (lines[i].contains('章节摘要') && i + 1 < lines.length) {
            summary = lines[i + 1].trim();
            break;
          }
        }
        if (summary.isNotEmpty) {
          outlineBuffer.writeln("- 第$chapterNum章：$summary");
        }
      }
      outlineBuffer.writeln();

      // 详细细纲信息
      for (final chapterNum in sortedChapterNumbers) {
        final content = allChapters[chapterNum]!;
        final chapterTitle = await novelMemory.getChapterTitle(chapterNum) ?? "第${chapterNum}章";

        // 标记章节位置关系
        if (chapterNum < currentChapterNumber) {
          outlineBuffer.writeln("### 第$chapterNum章：$chapterTitle（前文章节 - 已发生）");
          outlineBuffer.writeln("**状态：已完成，不可重复**");
        } else {
          outlineBuffer.writeln("### 第$chapterNum章：$chapterTitle（后续章节 - 计划中）");
          outlineBuffer.writeln("**状态：尚未发生，可作参考**");
        }

        outlineBuffer.writeln(content);
        outlineBuffer.writeln("---");
        outlineBuffer.writeln();
      }

      // 将已有细纲信息添加到 Memory 中
      // DetailedOutlineChain 现在已经配置了 Memory
      if (detailedChain.memory != null) {
        await detailedChain.memory.saveContext(
          inputValues: {
            detailedChain.memory.inputKey!: "系统：加载已有章节细纲（细纲生成模式）",
          },
          outputValues: {
            detailedChain.memory.outputKey!: outlineBuffer.toString(),
          },
        );

        print(
            "[LightweightGenerationService] 已有细纲信息已添加到 Memory，相关章节数: ${sortedChapterNumbers.length}");

        // 验证 Memory 中的内容
        final memoryVariables = await detailedChain.memory.loadMemoryVariables({
          detailedChain.memory.inputKey!: "验证",
        });

        if (memoryVariables.containsKey('history')) {
          final historyLength = memoryVariables['history'].toString().length;
          print(
              "[LightweightGenerationService] Memory 验证成功，当前历史记录长度: $historyLength");
        }
      } else {
        print(
            "[LightweightGenerationService] 错误：detailedChain 没有 memory 属性！");
      }
    } catch (e) {
      print("[LightweightGenerationService] 添加已有细纲信息到 Memory 失败: $e");
      // 不抛出异常，允许继续执行
    }
  }

  /// 使用AI修复格式错误的JSON内容
  ///
  /// 当AI生成的内容格式错误时，使用另一个AI调用来修复格式问题
  Future<String?> _fixJsonFormatWithAI(
      String malformedContent, String contentType) async {
    try {
      print("[LightweightGenerationService] 开始使用AI修复JSON格式错误...");

      // 获取当前模型配置
      final modelConfig = _apiConfigController.getCurrentModel();
      final llm = ModelAdapter.createLLMFromConfig(modelConfig);

      // 构建修复提示词
      String fixPrompt;
      switch (contentType) {
        case 'outline':
          fixPrompt = '''
你是一个专业的JSON格式修复助手。请将以下可能格式错误的内容转换为正确的JSON数组格式。

要求：
1. 输出必须是有效的JSON数组格式，以 [ 开始，以 ] 结束
2. 数组中的每个元素必须包含以下字段：
   - chapterNumber: 整数类型的章节编号
   - chapterTitle: 字符串类型的章节标题
   - summary: 字符串类型的章节摘要
3. 不要添加任何markdown标记（如 ```json）
4. 不要添加任何解释文字，只返回纯JSON数组
5. 确保所有字符串都用双引号包围
6. 确保JSON语法完全正确

需要修复的内容：
$malformedContent

请直接返回修复后的JSON数组：''';
          break;
        case 'detailed_outline':
          fixPrompt = '''
你是一个专业的文本格式修复助手。请将以下可能格式错误的章节细纲内容整理为规范的文本格式。

要求：
1. 保持原有的内容结构和信息
2. 修复可能的格式错误（如多余的符号、换行问题等）
3. 确保文本流畅易读
4. 不要添加任何markdown标记
5. 不要添加任何解释文字，只返回修复后的内容

需要修复的内容：
$malformedContent

请直接返回修复后的内容：''';
          break;
        case 'chapter_content':
          fixPrompt = '''
你是一个专业的小说内容修复助手。请将以下可能格式错误的章节内容整理为规范的小说文本格式。

要求：
1. 保持原有的故事内容和情节
2. 修复可能的格式错误（如多余的符号、markdown标记、JSON格式等）
3. 确保文本流畅易读，符合小说写作规范
4. 移除任何技术性标记（如```、json等）
5. 保持对话格式正确（使用"xxxx"某某说道的格式）
6. 不要添加任何解释文字，只返回修复后的小说内容
7. 如果内容过短，可以适当扩展，但要保持原有风格和情节方向
8. 确保段落分明，语言生动

需要修复的内容：
$malformedContent

请直接返回修复后的小说内容：''';
          break;
        default:
          fixPrompt = '''
你是一个专业的格式修复助手。请修复以下内容的格式错误，使其符合预期格式。

需要修复的内容：
$malformedContent

请直接返回修复后的内容：''';
      }

      // 调用AI进行修复
      final fixedContent = await llm.invoke(PromptValue.string(fixPrompt));
      final fixedResult = fixedContent.generations.first.output.content.trim();

      // 验证修复结果
      if (contentType == 'outline') {
        // 对于大纲，验证是否为有效JSON
        try {
          jsonDecode(fixedResult);
          print(
              "[LightweightGenerationService] AI修复JSON格式成功，长度: ${fixedResult.length}");
          return fixedResult;
        } catch (e) {
          print("[LightweightGenerationService] AI修复后的JSON仍然无效: $e");
          return null;
        }
      } else {
        // 对于其他类型，直接返回修复结果
        print(
            "[LightweightGenerationService] AI修复内容成功，长度: ${fixedResult.length}");
        return fixedResult;
      }
    } catch (e) {
      print("[LightweightGenerationService] AI修复过程中发生错误: $e");
      return null;
    }
  }

  /// 清理Grok模型输出的<think>标签
  String _cleanThinkTags(String content) {
    if (content.isEmpty) return content;

    String cleaned = content;

    // 移除<think>...</think>标签及其内容
    cleaned = cleaned.replaceAll(RegExp(r'<think>.*?</think>', dotAll: true), '');

    // 处理未闭合的<think>标签：查找<think>标签的位置
    final thinkStartIndex = cleaned.indexOf('<think>');
    if (thinkStartIndex != -1) {
      // 如果找到<think>标签，尝试保留标签前的内容
      final beforeThink = cleaned.substring(0, thinkStartIndex).trim();
      if (beforeThink.isNotEmpty) {
        cleaned = beforeThink;
      } else {
        // 如果<think>标签前没有内容，尝试查找标签后是否有有效的JSON
        final afterThink = cleaned.substring(thinkStartIndex + 7); // 跳过<think>
        final jsonMatch = RegExp(r'(\[.*\]|\{.*\})', dotAll: true).firstMatch(afterThink);
        if (jsonMatch != null) {
          cleaned = jsonMatch.group(1)!;
        } else {
          // 如果都没有，移除整个<think>部分
          cleaned = cleaned.replaceAll(RegExp(r'<think>.*$', dotAll: true), '');
        }
      }
    }

    // 清理多余的空白字符
    cleaned = cleaned.trim();

    // 如果清理后内容为空，返回原内容（避免过度清理）
    if (cleaned.isEmpty) {
      print("[LightweightGenerationService] 警告：清理<think>标签后内容为空，返回原内容");
      return content;
    }

    if (cleaned != content) {
      print("[LightweightGenerationService] 已清理<think>标签，原长度: ${content.length}, 清理后长度: ${cleaned.length}");
    }
    return cleaned;
  }
}
